<template>
  <div>
    <el-dialog title="编辑轮播图" width="40%" :destroy-on-close="true" :visible.sync="updateVisible" :before-close="handleClose">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <span class="search-title">
                图片:
                </span>
            <div style="width:100%">
              <el-form-item prop="image" style="margin-bottom:0">
                <el-upload
                    :file-list="fileList"
                    ref="upload"
                    :action="uploadImageUrl()"
                    accept="image/*"
                    :multiple="false"
                    :limit="1"
                    :before-upload="beforeAvatorUpload"
                    list-type="picture-card"
                    :on-preview="handlePictureCardPreview"
                    :on-exceed="handleExceed"
                    :on-success="handleAvatorSuccess"
                    :on-remove="handleRemove">
                  <i class="el-icon-plus"></i>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible">
                  <img width="100%" :src="dialogImageUrl" alt="">
                </el-dialog>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submit">确 定</el-button>
        <el-button size="mini" @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {mixin} from "../../../minix";
import {editSysRotations,getSysRotationsById} from '../../../api/api'
export default {
  mixins: [mixin],
  data() {
    return{
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      formLabelWidth: '80px',
      form: {
        image: "",
      },
      rules: {
        image: [
          { required: true, message: '请上传图片', trigger: 'blur' },
        ],
      },
    }
  },
  props: ['updateVisible','updateId'],
  methods: {
    handleAvatorSuccess(res) {
      let _this = this;
      if(res.code == 1000){
        _this.$message({
          type: 'success',
          message: '上传成功!'
        });
        this.fileList.push({url:this.$store.state.configure.HOST + res.message})
        this.form.image = res.message
      }else{
        _this.$notify.error({
          title: '错误',
          message: res.message
        });
      }
    },
    handleRemove(file, fileList) {
      this.form.content = ""
      this.fileList = []
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    submit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          editSysRotations(this.form).then(res => {
            if(res.code == 1000) {
              this.$notify.success({
                title: '成功',
                message: "保存成功"
              });
              this.handleClose();
            } else {
              this.$notify.error({
                title: '错误',
                message: res.message
              });
            }
          })
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.fileList = []
      this.$emit("updateFalse")
    },

  },
  created() {

  },
  mounted() {

  },
  watch: {
    updateId(newVal) {
      if(newVal) {
        getSysRotationsById({id:newVal}).then(res => {
          if(res.code == 1000) {
            this.form = res.data
            this.fileList.push({url:this.$store.state.configure.HOST + this.form.image})
          } else {
            this.$notify.error({
              title: '错误',
              message: res.message
            });
          }
        })
      }

    }
  }
}
</script>

<style lang=scss scoped>
.el-col {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 24px
}
.search-title {
  font-family: '黑体';
  float: right;
  white-space: nowrap;
  font-size: 14px;
  width: 84px;
  text-align: right;
}
.el-tree {
  border: 1px solid #BDC1C9;
}
</style>