{"name": "back_stage_front", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"axios": "^1.3.4", "clipboard": "^2.0.11", "core-js": "^3.8.3", "echarts": "^5.4.3", "element-plus": "^2.2.32", "element-ui": "^2.15.13", "nprogress": "^0.2.0", "vue": "^2.6.14", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.0", "@vue/cli-service": "^5.0.0", "sass": "^1.32.7", "sass-loader": "^12.0.0", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}