<template>
  <div>
    <el-dialog title="编辑房型" width="40%" :destroy-on-close="true" :visible.sync="updateVisible" :before-close="handleClose">
      <el-form :model="form" :rules="rules" ref="ruleForm">
        <el-row :gutter="10">
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <span class="search-title">
                名称:
                </span>
            <div style="width:100%">
              <el-form-item prop="name" style="margin-bottom:0">
                <el-input v-model="form.name" size="mini" placeholder="请输入名称" autocomplete="off"></el-input>
              </el-form-item>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <span class="search-title">
                价格:
                </span>
            <div style="width:100%">
              <el-form-item prop="price" style="margin-bottom:0">
                <el-input type="number" v-model="form.price" size="mini" placeholder="请输入价格" autocomplete="off"></el-input>
              </el-form-item>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <span class="search-title">
                数量:
                </span>
            <div style="width:100%">
              <el-form-item prop="num" style="margin-bottom:0">
                <el-input type="number" v-model="form.num" size="mini" placeholder="请输入数量" autocomplete="off"></el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="submit">确 定</el-button>
        <el-button size="mini" @click="handleClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {editSysHotelItem,getSysHotelItemById} from '../../../api/api'
export default {
  data() {
    return{
      formLabelWidth: '80px',
      form: {
        hotelId: "",
        name: "",
        price: "",
        num: "",
      },
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
        ],
        price: [
          { required: true, message: '请输入价格', trigger: 'blur' },
        ],
        num: [
          { required: true, message: '请输入数量', trigger: 'blur' },
        ],
      },
    }
  },
  props: ['updateVisible','updateId'],
  methods: {
    submit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          editSysHotelItem(this.form).then(res => {
            if(res.code == 1000) {
              this.$notify.success({
                title: '成功',
                message: "保存成功"
              });
              this.handleClose();
            } else {
              this.$notify.error({
                title: '错误',
                message: res.message
              });
            }
          })
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.$emit("updateFalse")
    },

  },
  created() {

  },
  mounted() {

  },
  watch: {
    updateId(newVal) {
      if(newVal) {
        getSysHotelItemById({id:newVal}).then(res => {
          if(res.code == 1000) {
            this.form = res.data
            this.form.num = res.data.num + ""
          } else {
            this.$notify.error({
              title: '错误',
              message: res.message
            });
          }
        })
      }

    }
  }
}
</script>

<style lang=scss scoped>
.el-col {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 24px
}
.search-title {
  font-family: '黑体';
  float: right;
  white-space: nowrap;
  font-size: 14px;
  width: 84px;
  text-align: right;
}
.el-tree {
  border: 1px solid #BDC1C9;
}
</style>